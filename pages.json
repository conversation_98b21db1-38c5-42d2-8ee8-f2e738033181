{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://doc.dcloud.net.cn/uni-app-x/collocation/pagesjson.html
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "uni-app x"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "login"
			}
		},
		{
			"path": "pages/login/wxLogin",
			"style": {
				"navigationBarTitleText": "微信登录",
				"navigationBarBackgroundColor": "#f8f8f8",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/login/wxAuth",
			"style": {
				"navigationBarTitleText": "授权登录",
				"navigationBarBackgroundColor": "#f8f8f8",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/crowdfunding/crowdfundingList/crowdfundingList",
			"style": {
				"navigationBarTitleText": "众筹列表",
				"navigationStyle": "custom",
				"backgroundColor": "#f5f5f5"
			}
		},
		{
			"path": "pages/example/api-example",
			"style": {
				"navigationBarTitleText": "API示例"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app x",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {}
}
