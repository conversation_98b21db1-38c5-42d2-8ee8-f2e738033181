// import { ResponseData } from "../../../api/types.uts"
// 直接定义，避免循环依赖
interface ResponseData {
    code: number
    message: string
    data?: any
}
// 登录请求参数接口
export interface LoginParams {
	username : string
	password : string
	captcha ?: string
	captchaId ?: string
}

// 登录响应数据接口
export interface LoginResponse extends ResponseData {
	data : {
		token : string
		refreshToken : string
		userInfo : UserInfo
		expires : number
	}
}

// 用户信息接口
export interface UserInfo {
	id : number
	username : string
	nickname : string
	avatar : string
	phone : string
	email : string
	status : number
	createTime : string
	updateTime : string
}

// 验证码响应接口
export interface CaptchaResponse extends ResponseData {
	data : {
		captchaId : string
		captchaImage : string
	}
}

// 刷新Token响应接口
export interface RefreshTokenResponse extends ResponseData {
	data : {
		token : string
		refreshToken : string
		expires : number
	}
}

// 微信code获取用户信息
export interface WxLoginParams {
	code? : string
}
// 微信登录响应接口
export interface WxLoginResponse extends LoginResponse {}