<template>
  <view class="crowdfunding-container">
    <!-- Tab切换 -->
    <view class="tab-container">
      <view class="tab-item" :class="{ active: currentTab === 'ongoing' }" @click="switchTab('ongoing')">
        <text class="tab-text">正在众筹</text>
      </view>
      <view class="tab-item" :class="{ active: currentTab === 'past' }" @click="switchTab('past')">
        <text class="tab-text">往期众筹</text>
      </view>
    </view>

    <!-- 众筹故事标题 -->
    <view class="story-header">
      <text class="story-title">众筹故事</text>
    </view>

    <!-- 众筹列表 -->
    <scroll-view class="list-container" scroll-y="true">
      <!-- 正在众筹列表 -->
      <view v-if="currentTab === 'ongoing'" class="ongoing-list">
        <!-- 特色产品展示 -->
        <view class="featured-product">
          <view class="featured-header">
            <view class="jd-logo">
              <text class="logo-text">京东京造</text>
            </view>
            <text class="featured-title">超轻秒学会<br/>光羽儿童自行车</text>
          </view>
          <view class="featured-content">
            <text class="featured-desc">超轻仅5.8kg* | 省力顺滑培林组件 | 正新CST轮胎</text>
            <view class="featured-image">
              <text class="bike-demo">👦👧🚲</text>
              <text class="demo-text">*5.8kg指16寸车全车重</text>
            </view>
          </view>
        </view>
        <view v-for="(item, index) in ongoingList" :key="index" class="crowdfunding-item">
          <view class="item-image">
            <view class="product-image" :style="{ backgroundColor: item.bgColor }">
              <view class="hot-tag" v-if="item.isHot">首发新品</view>
              <view class="bike-image" v-if="item.title === '光羽儿童自行车'">
                <text class="bike-icon">🚲</text>
              </view>
            </view>
          </view>
          <view class="item-content">
            <view class="brand-tag">自营</view>
            <text class="product-title">{{ item.title }}</text>
            <text class="product-desc">{{ item.desc }}</text>
            
            <view class="progress-section">
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: item.progress + '%' }"></view>
              </view>
              <view class="progress-info">
                <text class="progress-text">{{ item.progress }}%</text>
                <text class="raised-amount" v-if="item.raisedAmount">已筹金额 {{ item.raisedAmount }}</text>
              </view>
            </view>
            
            <view class="price-section">
              <text class="price-label">早鸟优惠价</text>
              <view class="size-options" v-if="item.title === '光羽儿童自行车'">
                <view class="size-item">
                  <text class="size-text">16寸</text>
                  <text class="size-price">¥699</text>
                </view>
                <view class="size-item">
                  <text class="size-text">20寸</text>
                  <text class="size-price">¥799</text>
                </view>
              </view>
              <view class="price-row">
                <text class="price">¥{{ item.price }}</text>
                <text class="price-unit">起</text>
                <button class="support-btn">去支持</button>
              </view>
            </view>
            
            <view class="bottom-info">
              <text class="time-info">距结束仅剩{{ item.remainDays }}天</text>
              <view class="support-info">
                <view class="avatar-group">
                  <view v-for="(avatar, i) in item.avatars" :key="i" class="avatar" :style="{ backgroundColor: avatar }"></view>
                </view>
                <text class="support-count">{{ item.supportCount }}人已支持</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 往期众筹列表 -->
      <view v-if="currentTab === 'past'" class="past-list">
        <view v-for="(item, index) in pastList" :key="index" class="past-item">
          <view class="item-image">
            <view class="product-image" :style="{ backgroundColor: item.bgColor }"></view>
          </view>
          <view class="item-content">
            <view class="brand-tag">自营</view>
            <text class="product-title">{{ item.title }}</text>
            
            <view class="progress-section">
              <view class="progress-bar completed">
                <view class="progress-fill" style="width: 100%"></view>
              </view>
              <text class="progress-text">{{ item.progress }}%</text>
            </view>
            
            <text class="support-info">{{ item.supportCount }}人支持 | 已完成</text>
            
            <view class="price-section">
              <text class="price">¥{{ item.price }}</text>
              <text class="original-price">到手价</text>
              <button class="detail-btn">了解项目</button>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="uts">
  import { ref } from 'vue'

  // 当前选中的tab
  const currentTab = ref('ongoing')
  interface OngoingListItem  {
	  title:string,
	  desc:string,
	  price:string,
	  progress:number,
	  remainDays:number,
	  supportCount:number,
	  isHot:boolean,
	  bgColor:string,
	  avatars:string[],
	  raisedAmount?:string
  }
  // 正在众筹数据
  const ongoingList = ref<OngoingListItem[]>([
    {
      title: '光羽儿童自行车',
      desc: '超轻5.8kG 秒学会 大牌正新轮胎 防滑更安全 培林组件 顺滑超省力',
      price: '699',
      progress: 174,
      remainDays: 1, // 距结束仅剩1天（07.31 23:）
      supportCount: 224,
      isHot: true,
      bgColor: '#e8f5e8',
      avatars: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
      raisedAmount: '174,090'
    },
    {
      title: '1号店人体工学坐垫',
      desc: '人体工学设计舒适坐垫',
      price: '129',
      progress: 40,
      remainDays: 15,
      supportCount: 68,
      isHot: false,
      bgColor: '#f0f0f0',
      avatars: ['#ff9ff3', '#54a0ff', '#5f27cd']
    },
    {
      title: '京东京造Q弹垫',
      desc: '腰部MINI弹簧对护腰',
      price: '299',
      progress: 108,
      remainDays: 4,
      supportCount: 100,
      isHot: false,
      bgColor: '#fff2e6',
      avatars: ['#ff6348', '#2ed573', '#3742fa']
    },
    {
      title: '京造变频热泵烘干机',
      desc: '童颜变频热泵烘干机',
      price: '2999',
      progress: 22,
      remainDays: 12,
      supportCount: 45,
      isHot: false,
      bgColor: '#f8f8f8',
      avatars: ['#ff4757', '#2ed573', '#5352ed']
    }
  ])
  interface PostListItem {
	  title:string,
	  price:string,
	  progress:number,
	  supportCount:number,
	  bgColor:string
  }
  // 往期众筹数据
  const pastList = ref<PostListItem[]>([
    {
      title: '京造大吸力油烟机',
      price: '2999',
      progress: 3793,
      supportCount: 2950,
      bgColor: '#2c2c2c'
    },
    {
      title: '京东京造猛火灶具',
      price: '1999',
      progress: 1297,
      supportCount: 2180,
      bgColor: '#4a4a4a'
    },
    {
      title: '京造机械臂洗地机',
      price: '2199',
      progress: 671,
      supportCount: 3374,
      bgColor: '#1a1a2e'
    },
    {
      title: '微风1号店联名电竞椅',
      price: '899',
      progress: 225,
      supportCount: 58,
      bgColor: '#0f3460'
    },
    {
      title: '微风电竞椅带脚托',
      price: '1299',
      progress: 101,
      supportCount: 89,
      bgColor: '#16213e'
    }
  ])

  // 切换tab
  const switchTab = (tab: string) => {
    currentTab.value = tab
  }

  // 返回
  const goBack = () => {
    uni.navigateBack()
  }
</script>

<style lang="scss">
.crowdfunding-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.tab-container {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #eee;

  .tab-item {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .tab-text {
      font-size: 32rpx;
      color: #666;
    }

    &.active {
      .tab-text {
        color: #ff6600;
        font-weight: 500;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #ff6600;
        border-radius: 2rpx;
      }
    }
  }
}

.story-header {
  background-color: white;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;

  .story-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
  }
}

.list-container {
  flex: 1;
  padding: 24rpx;
}

.featured-product {
  background: linear-gradient(135deg, #e8f5ff 0%, #f0f8ff 100%);
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  padding: 32rpx;
  overflow: hidden;

  .featured-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .jd-logo {
      background-color: #ff4444;
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;

      .logo-text {
        font-size: 24rpx;
        font-weight: bold;
      }
    }

    .featured-title {
      font-size: 48rpx;
      font-weight: bold;
      color: #333;
      text-align: right;
      line-height: 1.2;
    }
  }

  .featured-content {
    .featured-desc {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 24rpx;
      display: block;
    }

    .featured-image {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .bike-demo {
        font-size: 80rpx;
      }

      .demo-text {
        font-size: 20rpx;
        color: #999;
      }
    }
  }
}

.crowdfunding-item {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  display: flex;
  gap: 24rpx;

  .item-image {
    .product-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 12rpx;
      position: relative;

      .hot-tag {
        position: absolute;
        top: 8rpx;
        left: 8rpx;
        background-color: #333;
        color: white;
        font-size: 20rpx;
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
      }

      .bike-image {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        .bike-icon {
          font-size: 60rpx;
          color: #666;
        }
      }
    }
  }

  .item-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .brand-tag {
      background-color: #ff4444;
      color: white;
      font-size: 20rpx;
      padding: 4rpx 12rpx;
      border-radius: 8rpx;
      align-self: flex-start;
      margin-bottom: 12rpx;
    }

    .product-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }

    .product-desc {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 16rpx;
    }
  }
}

.progress-section {
  margin-bottom: 16rpx;

  .progress-bar {
    height: 8rpx;
    background-color: #f0f0f0;
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 8rpx;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #ff6600 0%, #ffaa00 100%);
      border-radius: 4rpx;
      transition: width 0.3s ease;
    }

    &.completed .progress-fill {
      background: linear-gradient(90deg, #00c853 0%, #4caf50 100%);
    }
  }

  .progress-info {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .progress-text {
      font-size: 24rpx;
      color: #ff6600;
    }

    .raised-amount {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.price-section {
  margin-bottom: 16rpx;

  .price-label {
    font-size: 24rpx;
    color: #666;
    display: block;
    margin-bottom: 8rpx;
  }

  .size-options {
    display: flex;
    gap: 16rpx;
    margin-bottom: 12rpx;

    .size-item {
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;

      .size-text {
        font-size: 24rpx;
        color: #333;
      }

      .size-price {
        font-size: 28rpx;
        color: #ff4444;
        font-weight: bold;
      }
    }
  }

  .price-row {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .price {
      font-size: 36rpx;
      color: #ff4444;
      font-weight: bold;
    }

    .price-unit {
      font-size: 24rpx;
      color: #ff4444;
      margin-left: 8rpx;
    }

    .original-price {
      font-size: 24rpx;
      color: #999;
      margin-left: 8rpx;
    }

    .support-btn, .detail-btn {
      background-color: #ff6600;
      color: white;
      font-size: 24rpx;
      padding: 12rpx 32rpx;
      border-radius: 32rpx;
      border: none;

      &:active {
        background-color: #e55a00;
      }
    }
  }
}

.bottom-info {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .time-info {
    font-size: 24rpx;
    color: #999;
  }

  .support-info {
    display: flex;
    align-items: center;
    gap: 12rpx;

    .avatar-group {
      display: flex;
      gap: -8rpx;

      .avatar {
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        border: 2rpx solid white;
        margin-left: -8rpx;

        &:first-child {
          margin-left: 0;
        }
      }
    }

    .support-count {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.past-item {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  display: flex;
  gap: 24rpx;

  .item-image {
    .product-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 12rpx;
    }
  }

  .item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .brand-tag {
      background-color: #ff4444;
      color: white;
      font-size: 20rpx;
      padding: 4rpx 12rpx;
      border-radius: 8rpx;
      align-self: flex-start;
    }

    .product-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .support-info {
      font-size: 24rpx;
      color: #999;
    }

    .price-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: auto;

      .price {
        font-size: 36rpx;
        color: #ff4444;
        font-weight: bold;
      }
    }
  }
}
</style>
